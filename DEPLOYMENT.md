# Fly.io Deployment Guide

This guide provides step-by-step instructions for setting up automated deployment to Fly.io via GitHub Actions.

## Prerequisites

1. [Fly.io account](https://fly.io/app/sign-up)
2. [Fly.io CLI installed](https://fly.io/docs/hands-on/install-flyctl/)
3. GitHub repository with admin access

## Initial Fly.io Setup

### 1. Install and Login to Fly.io CLI

```bash
# Install flyctl (if not already installed)
curl -L https://fly.io/install.sh | sh

# Login to your Fly.io account
flyctl auth login
```

### 2. Create Fly.io Applications

```bash
# Create main Reflex application
flyctl apps create reflex-chat-bruno-main --org personal

# Create FastAPI service application  
flyctl apps create reflex-chat-bruno-api --org personal
```

### 3. Create PostgreSQL Database

```bash
# Create PostgreSQL cluster
flyctl postgres create --name reflex-chat-bruno-main-db --org personal --region iad --vm-size shared-cpu-1x --volume-size 10

# Attach database to both applications
flyctl postgres attach reflex-chat-bruno-main-db --app reflex-chat-bruno-main
flyctl postgres attach reflex-chat-bruno-main-db --app reflex-chat-bruno-api
```

### 4. Create Redis Instance

```bash
# Create Redis instance
flyctl redis create --name reflex-chat-bruno-main-redis --org personal --region iad --plan free

# Attach Redis to both applications
flyctl redis connect reflex-chat-bruno-main-redis --app reflex-chat-bruno-main
flyctl redis connect reflex-chat-bruno-main-redis --app reflex-chat-bruno-api
```

## Environment Variables Setup

### 1. Set Secrets for Main App

```bash
# Authentication
flyctl secrets set TENANT_ID="your_tenant_id" --app reflex-chat-bruno-main
flyctl secrets set CLIENT_ID="your_client_id" --app reflex-chat-bruno-main
flyctl secrets set CLIENT_SECRET="your_client_secret" --app reflex-chat-bruno-main
flyctl secrets set REDIRECT_URI="https://reflex-chat-bruno-main.fly.dev/auth/callback" --app reflex-chat-bruno-main

# OpenAI
flyctl secrets set OPENAI_API_KEY="your_openai_api_key" --app reflex-chat-bruno-main
flyctl secrets set OPENAI_MODEL="gpt-4-turbo" --app reflex-chat-bruno-main

# API URLs
flyctl secrets set FASTAPI_URL="https://reflex-chat-bruno-api.fly.dev" --app reflex-chat-bruno-main
flyctl secrets set API_URL="https://reflex-chat-bruno-main.fly.dev" --app reflex-chat-bruno-main
flyctl secrets set WEBSOCKET_URL="wss://reflex-chat-bruno-main.fly.dev" --app reflex-chat-bruno-main

# User data
flyctl secrets set USER_AZURE_ID="your_user_azure_id" --app reflex-chat-bruno-main
```

### 2. Set Secrets for FastAPI App

```bash
# Authentication (same as main app)
flyctl secrets set TENANT_ID="your_tenant_id" --app reflex-chat-bruno-api
flyctl secrets set CLIENT_ID="your_client_id" --app reflex-chat-bruno-api
flyctl secrets set CLIENT_SECRET="your_client_secret" --app reflex-chat-bruno-api

# OpenAI
flyctl secrets set OPENAI_API_KEY="your_openai_api_key" --app reflex-chat-bruno-api
flyctl secrets set OPENAI_MODEL="gpt-4-turbo" --app reflex-chat-bruno-api

# Search (if using Azure Search)
flyctl secrets set SEARCH_SERVICE_NAME="your_search_service_name" --app reflex-chat-bruno-api
flyctl secrets set SEARCH_API_KEY="your_search_api_key" --app reflex-chat-bruno-api
flyctl secrets set BLOB_CONNECTION_STRING="your_blob_connection_string" --app reflex-chat-bruno-api
flyctl secrets set BLOB_CONTAINER_NAME="your_container_name" --app reflex-chat-bruno-api
flyctl secrets set INDEX_NAME="your_index_name" --app reflex-chat-bruno-api

# User data
flyctl secrets set USER_AZURE_ID="your_user_azure_id" --app reflex-chat-bruno-api
```

## GitHub Actions Setup

### 1. Get Fly.io API Token

```bash
# Generate a new API token
flyctl auth token
```

### 2. Add GitHub Secrets

Go to your GitHub repository → Settings → Secrets and variables → Actions

Add the following secret:
- `FLY_API_TOKEN`: The token from step 1

## Manual First Deployment

Before enabling automatic deployments, perform a manual deployment to ensure everything works:

```bash
# Deploy FastAPI service first
flyctl deploy --config fly-fastapi.toml

# Deploy main application
flyctl deploy --config fly.toml
```

## Database Initialization

After first deployment, initialize the database:

```bash
# Connect to the main app
flyctl ssh console --app reflex-chat-bruno-main

# Inside the container, run:
python -c "from Reflex_Chat.database.db import create_db_and_tables; create_db_and_tables()"
python -c "from Reflex_Chat.database.seeds.seed_data import *; seed_roles(); seed_performance_competencies(); seed_potential_competencies(); seed_competency_role_map_from_json(); seed_users_and_projects(); seed_evaluations(); seed_action_plans()"
```

## Verification

### 1. Check Application Status

```bash
# Check main app
flyctl status --app reflex-chat-bruno-main

# Check FastAPI service
flyctl status --app reflex-chat-bruno-api
```

### 2. View Logs

```bash
# Main app logs
flyctl logs --app reflex-chat-bruno-main

# FastAPI logs  
flyctl logs --app reflex-chat-bruno-api
```

### 3. Test Applications

- Main app: https://reflex-chat-bruno-main.fly.dev
- FastAPI service: https://reflex-chat-bruno-api.fly.dev

## Automatic Deployments

Once manual deployment works, automatic deployments will trigger on every push to the `main` branch.

## Local Development Verification

Ensure your local environment still works after adding deployment files:

```bash
# Test local development
docker-compose down
docker-compose up -d

# Verify services
curl http://localhost:3000
curl http://localhost:8000
curl http://localhost:8001
```

## Troubleshooting

### Common Issues

1. **Database connection errors**: Check that DATABASE_URL is set correctly
2. **Redis connection errors**: Verify REDIS_URL is configured
3. **Authentication issues**: Ensure REDIRECT_URI matches your Fly.io domain
4. **API communication**: Verify FASTAPI_URL points to the correct Fly.io app

### Useful Commands

```bash
# View app configuration
flyctl config show --app reflex-chat-bruno-main

# View secrets (names only)
flyctl secrets list --app reflex-chat-bruno-main

# Scale application
flyctl scale count 2 --app reflex-chat-bruno-main

# View machine status
flyctl machine list --app reflex-chat-bruno-main
```

## Rollback Instructions

If deployment fails, you can rollback:

```bash
# List releases
flyctl releases --app reflex-chat-bruno-main

# Rollback to previous release
flyctl releases rollback --app reflex-chat-bruno-main
```

## Cost Optimization

- Both apps are configured with minimal resources (512MB-1GB RAM)
- Auto-stop/start is enabled to reduce costs when not in use
- Free tier PostgreSQL and Redis are used

## Security Notes

- All sensitive data is stored as Fly.io secrets
- HTTPS is enforced for all connections
- Database and Redis are only accessible within the Fly.io private network
