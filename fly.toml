# Fly.io configuration for main Reflex application
app = "reflex-chat-main"
primary_region = "iad"

[build]
  dockerfile = "Dockerfile.production"

[env]
  PYTHONUNBUFFERED = "1"
  FRONTEND_PORT = "3000"
  BACKEND_PORT = "8000"
  REFLEX_CONFIG = "rxconfig.production"

[http_service]
  internal_port = 3000
  force_https = true
  auto_stop_machines = false
  auto_start_machines = true
  min_machines_running = 1
  processes = ["app"]

  [[http_service.checks]]
    grace_period = "30s"
    interval = "30s"
    method = "GET"
    timeout = "10s"
    path = "/"

[http_service.concurrency]
  type = "connections"
  hard_limit = 1000
  soft_limit = 500

# Backend service on port 8000
[[services]]
  protocol = "tcp"
  internal_port = 8000

  [[services.ports]]
    port = 8000
    handlers = ["http"]
    force_https = true

  [[services.tcp_checks]]
    grace_period = "30s"
    interval = "30s"
    timeout = "10s"

[machine]
  memory = "1gb"
  cpu_kind = "shared"
  cpus = 1

[[vm]]
  memory = "1gb"
  cpu_kind = "shared"
  cpus = 1
