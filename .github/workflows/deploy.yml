name: Deploy to Fly.io

on:
  push:
    branches: [ main ]
  workflow_dispatch:

jobs:
  deploy:
    name: Deploy to Fly.io
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Fly.io CLI
      uses: superfly/flyctl-actions/setup-flyctl@master

    - name: Deploy FastAPI Service
      run: |
        echo "Deploying FastAPI service..."
        flyctl deploy --config fly-fastapi.toml --remote-only
      env:
        FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}

    - name: Wait for FastAPI deployment
      run: sleep 30

    - name: Deploy Main Reflex App
      run: |
        echo "Deploying main Reflex application..."
        flyctl deploy --config fly.toml --remote-only
      env:
        FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}

    - name: Verify deployments
      run: |
        echo "Verifying FastAPI deployment..."
        flyctl status --app reflex-chat-api
        echo "Verifying main app deployment..."
        flyctl status --app reflex-chat-main
      env:
        FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}
