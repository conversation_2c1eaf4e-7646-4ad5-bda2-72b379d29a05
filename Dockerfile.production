FROM python:3.11-slim

# Set working directory inside container
WORKDIR /app

# Install required system packages
RUN apt-get update && apt-get install -y gcc libpq-dev curl unzip && \
    rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy production config first
COPY rxconfig.production.py rxconfig.py

# Copy your local code
COPY . .

# Set build environment variable to skip runtime checks
ENV REFLEX_BUILD_ENV=true

# Build the frontend for production deployments
RUN reflex init

# Export the frontend for production
RUN reflex export --frontend-only --no-zip

# Unset build environment variable for runtime
ENV REFLEX_BUILD_ENV=false

# Expose ports
EXPOSE 3000 8000

# Production command
CMD ["reflex", "run", "--env", "prod", "--frontend-port", "3000", "--backend-port", "8000"]
