import reflex as rx
import os
from Reflex_Chat.pages.evaluations import evaluations_performance, evaluations_performance_start
from Reflex_Chat.pages.dashboard import dashboard
from rxconfig import config
from Reflex_Chat.state import AuthState, sso_app, AUTHORITY
from Reflex_Chat.pages import chat, home, login, logout, auth_callback
from Reflex_Chat.components.navbar import navbar
from Reflex_Chat.database.db import create_db_and_tables, reset_db_and_tables

# Check if we're in a build environment
IS_BUILD_ENV = os.getenv("REFLEX_BUILD_ENV") == "true"

# For development: Uncomment to reset the database (WARNING: This deletes all data)
#reset_db_and_tables()

# For normal operation: Create tables if they don't exist (skip during build)
if not IS_BUILD_ENV:
    create_db_and_tables()
else:
    print("Skipping database initialization in build environment")

# Set FastAPI URL for API calls
FASTAPI_URL = os.getenv("FASTAPI_URL", "http://fastapi:8001")

# Initialize Reflex App
app = rx.App()

# Runtime database initialization (called after app starts)
def initialize_database_at_runtime():
    """Initialize database tables at runtime (not during build)."""
    if not IS_BUILD_ENV:
        try:
            print("Initializing database at runtime...")
            create_db_and_tables()
            print("Database initialization completed successfully")
        except Exception as e:
            print(f"Warning: Database initialization failed: {e}")
            # Don't fail the app startup if database init fails
    else:
        print("Skipping runtime database initialization in build environment")

# Call database initialization when the app starts (not during build)
if not IS_BUILD_ENV:
    initialize_database_at_runtime()













