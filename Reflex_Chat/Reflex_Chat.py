import reflex as rx
import os
from Reflex_Chat.pages.evaluations import evaluations_performance, evaluations_performance_start
from Reflex_Chat.pages.dashboard import dashboard
from rxconfig import config
from Reflex_Chat.state import AuthState, sso_app, AUTHORITY
from Reflex_Chat.pages import chat, home, login, logout, auth_callback
from Reflex_Chat.components.navbar import navbar

# CRITICAL: NO DATABASE OPERATIONS DURING MODULE IMPORT
# All database operations are deferred to runtime events only

# Check if we're in a build environment
IS_BUILD_ENV = os.getenv("REFLEX_BUILD_ENV", "").lower() == "true"

# Set FastAPI URL for API calls
FASTAPI_URL = os.getenv("FASTAPI_URL", "http://fastapi:8001")

# Initialize Reflex App - NO DATABASE OPERATIONS HERE
app = rx.App()

# Database initialization is completely deferred to runtime startup events
# NO database connections, imports, or operations during module import

# Define a startup event for database initialization
@rx.event
def initialize_database_on_startup():
    """Initialize database when the app starts up (not during import)."""
    if IS_BUILD_ENV:
        return

    try:
        # Lazy import to avoid import-time database connections
        from Reflex_Chat.database.db import create_db_and_tables
        create_db_and_tables()
    except Exception as e:
        print(f"Database initialization failed: {e}")

# Note: The startup event will be triggered when the app actually starts serving requests
# This ensures database initialization happens at runtime, not during module import













