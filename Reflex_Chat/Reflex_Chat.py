import reflex as rx
import os
from Reflex_Chat.pages.evaluations import evaluations_performance, evaluations_performance_start
from Reflex_Chat.pages.dashboard import dashboard
from rxconfig import config
from Reflex_Chat.state import AuthState, sso_app, AUTHORITY
from Reflex_Chat.pages import chat, home, login, logout, auth_callback
from Reflex_Chat.components.navbar import navbar

# CRITICAL: NO DATABASE OPERATIONS DURING MODULE IMPORT
# All database operations are deferred to runtime events only

# Check if we're in a build environment
IS_BUILD_ENV = os.getenv("REFLEX_BUILD_ENV", "").lower() == "true"

# Set FastAPI URL for API calls
FASTAPI_URL = os.getenv("FASTAPI_URL", "http://fastapi:8001")

# Initialize Reflex App - NO DATABASE OPERATIONS HERE
app = rx.App()

# Database initialization is completely deferred to runtime startup events
# NO database connections, imports, or operations during module import
print(f"[STARTUP] Reflex app initialized. Build environment: {IS_BUILD_ENV}")
print("[STARTUP] All database operations deferred to runtime events")

# Add debugging to catch any unexpected database calls
import sys
import traceback

def debug_database_import():
    """Debug function to track if any database modules are imported during startup."""
    db_modules = [name for name in sys.modules.keys() if 'database' in name.lower() or 'sql' in name.lower()]
    if db_modules:
        print(f"[DEBUG] Database-related modules loaded during import: {db_modules}")
        print("[DEBUG] Call stack:")
        traceback.print_stack()
    else:
        print("[DEBUG] No database modules loaded during import - GOOD!")

debug_database_import()

# Add runtime PostgreSQL driver verification
def verify_postgres_driver():
    """Verify that PostgreSQL driver is available at runtime."""
    try:
        import psycopg2
        print(f"[RUNTIME] ✓ psycopg2 available: {psycopg2.__version__}")
        return True
    except ImportError as e:
        print(f"[RUNTIME] ✗ psycopg2 not available: {e}")
        return False

def verify_sqlalchemy_postgres():
    """Verify that SQLAlchemy PostgreSQL dialect is available at runtime."""
    try:
        from sqlalchemy.dialects import postgresql
        print("[RUNTIME] ✓ SQLAlchemy PostgreSQL dialect available")
        return True
    except ImportError as e:
        print(f"[RUNTIME] ✗ SQLAlchemy PostgreSQL dialect not available: {e}")
        return False

# Only verify drivers if not in build environment
if not IS_BUILD_ENV:
    print("[RUNTIME] Verifying PostgreSQL drivers...")
    psycopg2_ok = verify_postgres_driver()
    sqlalchemy_ok = verify_sqlalchemy_postgres()

    if psycopg2_ok and sqlalchemy_ok:
        print("[RUNTIME] ✓ All PostgreSQL drivers verified successfully")
    else:
        print("[RUNTIME] ✗ PostgreSQL driver verification failed")
        print("[RUNTIME] Database operations will be disabled")
else:
    print("[BUILD] Skipping runtime driver verification in build environment")

# Define a startup event for database initialization
@rx.event
def initialize_database_on_startup():
    """Initialize database when the app starts up (not during import)."""
    if IS_BUILD_ENV:
        print("[STARTUP] Skipping database initialization in build environment")
        return

    print("[STARTUP] Initializing database at runtime...")
    try:
        # Lazy import to avoid import-time database connections
        from Reflex_Chat.database.db import create_db_and_tables
        create_db_and_tables()
        print("[STARTUP] ✓ Database initialization completed successfully")
    except Exception as e:
        print(f"[STARTUP] ✗ Database initialization failed: {e}")
        print("[STARTUP] Application will continue without database functionality")

# Note: The startup event will be triggered when the app actually starts serving requests
# This ensures database initialization happens at runtime, not during module import













