import reflex as rx
import os
from Reflex_Chat.pages.evaluations import evaluations_performance, evaluations_performance_start
from Reflex_Chat.pages.dashboard import dashboard
from rxconfig import config
from Reflex_Chat.state import AuthState, sso_app, AUTHORITY
from Reflex_Chat.pages import chat, home, login, logout, auth_callback
from Reflex_Chat.components.navbar import navbar
from Reflex_Chat.database.db import create_db_and_tables, reset_db_and_tables

# Check if we're in a build environment
IS_BUILD_ENV = os.getenv("REFLEX_BUILD_ENV", "").lower() == "true"

# For development: Uncomment to reset the database (WARNING: This deletes all data)
#reset_db_and_tables()

# Database initialization will be handled at runtime startup, not during import
print(f"Build environment: {IS_BUILD_ENV}")
print("Database initialization deferred to runtime startup")

# Set FastAPI URL for API calls
FASTAPI_URL = os.getenv("FASTAPI_URL", "http://fastapi:8001")

# Initialize Reflex App
app = rx.App()

# Database initialization will be handled by a startup event
# This ensures it happens after the app is fully initialized and running













