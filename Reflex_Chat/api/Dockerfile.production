FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better caching
COPY Reflex_Chat/api/requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy the entire Reflex_Chat directory to maintain structure
COPY Reflex_Chat/ /app/Reflex_Chat/

# Set working directory to the API directory
WORKDIR /app/Reflex_Chat/api

# Add the parent directory to Python path for imports
ENV PYTHONPATH=/app

# Expose port
EXPOSE 8001

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:8001/ || exit 1

# Create startup script to verify module availability
RUN echo '#!/bin/bash\n\
echo "Verifying Python environment..."\n\
echo "Current directory: $(pwd)"\n\
echo "Python path: $PYTHONPATH"\n\
echo "Files in current directory:"\n\
ls -la\n\
echo "Checking if main.py exists:"\n\
ls -la main.py\n\
echo "Testing main module import:"\n\
python -c "import main; print(\"✓ main module imported successfully\")"\n\
echo "Starting uvicorn..."\n\
exec uvicorn main:app --host 0.0.0.0 --port 8001 --workers 1\n\
' > /app/start.sh && chmod +x /app/start.sh

# Run the application with verification
CMD ["/app/start.sh"]
