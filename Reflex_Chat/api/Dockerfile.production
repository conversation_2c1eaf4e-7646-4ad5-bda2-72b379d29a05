FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better caching
COPY Reflex_Chat/api/requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy the entire Reflex_Chat directory to maintain structure
COPY Reflex_Chat/ /app/Reflex_Chat/

# Copy the API code to the working directory
COPY Reflex_Chat/api/ .

# Add the Reflex_Chat directory to Python path
ENV PYTHONPATH=/app

# Expose port
EXPOSE 8001

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8001/ || exit 1

# Run the application
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8001", "--workers", "1"]
