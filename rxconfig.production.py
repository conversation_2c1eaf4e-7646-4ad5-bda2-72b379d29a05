import reflex as rx
import os

# Production configuration for Fly.io deployment
config = rx.Config(
    app_name="Reflex_Chat",
    state_manager_lock_expiration=30000,  # 30 seconds lock expiration
    frontend_port=3000,
    backend_port=8000,
    backend_host="0.0.0.0",  # Bind to all interfaces for Fly.io
    frontend_host="0.0.0.0",  # Bind frontend to all interfaces for Fly.io
    # Production URLs - override any localhost URLs from .env
    api_url="https://reflex-chat-main.fly.dev:8000",
    websocket_url="wss://reflex-chat-main.fly.dev:8000",
    # Enable production optimizations
    compile=True,
    # Database and Redis URLs from environment
    db_url=os.getenv("DATABASE_URL"),
    redis_url=os.getenv("REDIS_URL"),
)
